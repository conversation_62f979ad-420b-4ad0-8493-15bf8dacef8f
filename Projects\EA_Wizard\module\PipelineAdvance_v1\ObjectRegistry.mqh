#property strict

#include "../mql4-lib-master/Collection/HashMap.mqh"

#define OBJECT_REGISTRY_DEFAULT_NAME "ObjectRegistry"
#define OBJECT_REGISTRY_DEFAULT_TYPE "ObjectRegistry"

//+------------------------------------------------------------------+
//| ObjectRegistry 類 - 以 HashMap 作為核心成員的對象註冊器         |
//| 核心方法：Register 和 Unregister                                |
//+------------------------------------------------------------------+
class ObjectRegistry
{
private:
    HashMap<string, void*>* m_objects;      // 核心 HashMap 成員
    string m_name;                          // 註冊器名稱
    string m_type;                          // 註冊器類型
    bool m_owned;                           // 是否擁有對象的所有權
    string m_lastRegisteredKey;             // 最後註冊的鍵

public:
    // 構造函數
    ObjectRegistry(string name = OBJECT_REGISTRY_DEFAULT_NAME, 
                   string type = OBJECT_REGISTRY_DEFAULT_TYPE, 
                   bool owned = false)
        : m_name(name),
          m_type(type),
          m_owned(owned),
          m_lastRegisteredKey("")
    {
        m_objects = new HashMap<string, void*>(NULL, m_owned);
    }

    // 析構函數
    virtual ~ObjectRegistry()
    {
        if(m_objects != NULL)
        {
            delete m_objects;
            m_objects = NULL;
        }
    }

    // 核心方法：註冊對象
    virtual bool Register(const string key, void* object)
    {
        if(m_objects == NULL)
        {
            return false;
        }
        
        if(key == "" || object == NULL)
        {
            return false;
        }
        
        // 檢查是否已存在
        if(m_objects.contains(key))
        {
            return false;
        }
        
        // 註冊對象
        m_objects.set(key, object);
        m_lastRegisteredKey = key;
        
        return true;
    }

    // 核心方法：移除對象
    virtual bool Unregister(const string key)
    {
        if(m_objects == NULL)
        {
            return false;
        }
        
        if(key == "")
        {
            return false;
        }
        
        // 檢查是否存在
        if(!m_objects.contains(key))
        {
            return false;
        }
        
        // 移除對象
        return m_objects.remove(key);
    }

    // 獲取對象
    virtual void* GetObject(const string key)
    {
        if(m_objects == NULL)
        {
            return NULL;
        }
        
        if(key == "")
        {
            return NULL;
        }
        
        if(!m_objects.contains(key))
        {
            return NULL;
        }
        
        return m_objects.get(key, NULL);
    }

    // 檢查是否包含指定鍵
    virtual bool Contains(const string key)
    {
        if(m_objects == NULL)
        {
            return false;
        }
        
        return m_objects.contains(key);
    }

    // 獲取對象數量
    virtual int GetCount()
    {
        if(m_objects == NULL)
        {
            return 0;
        }
        
        return m_objects.size();
    }

    // 清空所有對象
    virtual void Clear()
    {
        if(m_objects != NULL)
        {
            m_objects.clear();
        }
        m_lastRegisteredKey = "";
    }

    // 獲取所有鍵
    virtual int GetAllKeys(string &keys[])
    {
        if(m_objects == NULL)
        {
            ArrayResize(keys, 0);
            return 0;
        }
        
        int count = m_objects.size();
        if(count == 0)
        {
            ArrayResize(keys, 0);
            return 0;
        }
        
        ArrayResize(keys, count);
        
        // 使用迭代器獲取所有鍵
        MapIterator<string, void*>* iterator = m_objects.iterator();
        int index = 0;
        
        while(!iterator.end() && index < count)
        {
            keys[index] = iterator.key();
            iterator.next();
            index++;
        }
        
        delete iterator;
        return index;
    }

    // 獲取註冊器名稱
    virtual string GetName()
    {
        return m_name;
    }

    // 獲取註冊器類型
    virtual string GetType()
    {
        return m_type;
    }

    // 檢查是否為空
    virtual bool IsEmpty()
    {
        if(m_objects == NULL)
        {
            return true;
        }
        
        return m_objects.isEmpty();
    }

    // 獲取最後註冊的鍵
    virtual string GetLastRegisteredKey()
    {
        return m_lastRegisteredKey;
    }

    // 檢查是否擁有對象所有權
    virtual bool IsOwned()
    {
        return m_owned;
    }

    // 設置註冊器名稱
    virtual void SetName(const string name)
    {
        m_name = name;
    }

    // 設置註冊器類型
    virtual void SetType(const string type)
    {
        m_type = type;
    }

    // 更新對象（如果鍵存在）
    virtual bool UpdateObject(const string key, void* object)
    {
        if(m_objects == NULL)
        {
            return false;
        }
        
        if(key == "" || object == NULL)
        {
            return false;
        }
        
        // 檢查是否存在
        if(!m_objects.contains(key))
        {
            return false;
        }
        
        // 更新對象
        m_objects.set(key, object);
        return true;
    }

    // 註冊或更新對象
    virtual bool RegisterOrUpdate(const string key, void* object)
    {
        if(m_objects == NULL)
        {
            return false;
        }
        
        if(key == "" || object == NULL)
        {
            return false;
        }
        
        // 直接設置（如果存在則更新，不存在則新增）
        m_objects.set(key, object);
        m_lastRegisteredKey = key;
        
        return true;
    }

    // 獲取 HashMap 實例（用於高級操作）
    virtual HashMap<string, void*>* GetHashMap()
    {
        return m_objects;
    }
};
